/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features for better performance
  experimental: {
    // Enable server components by default
    serverComponentsExternalPackages: [],
  },

  // API rewrites for proxying to backend services
  async rewrites() {
    const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8300';
    
    return [
      // Health check endpoint - direct proxy to backend
      {
        source: '/api/health',
        destination: `${backendUrl}/health`,
      },
      // Public API endpoints - direct proxy to backend
      {
        source: '/api/public/:path*',
        destination: `${backendUrl}/api/public/:path*`,
      },
      // All other API routes go through Next.js API routes for authentication
      // This allows our middleware to handle auth, CORS, etc.
    ];
  },

  // Headers configuration for CORS and security
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.NODE_ENV === 'production' 
              ? process.env.FRONTEND_URL || 'https://yourdomain.com'
              : '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, X-Requested-With',
          },
          {
            key: 'Access-Control-Max-Age',
            value: '86400',
          },
        ],
      },
    ];
  },

  // Optimize images
  images: {
    domains: ['localhost'],
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8300',
        pathname: '/uploads/**',
      },
    ],
  },

  // Webpack configuration for better bundling
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Don't bundle server-only modules on the client
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    return config;
  },

  // Environment variables to expose to the client
  env: {
    BACKEND_API_URL: process.env.BACKEND_API_URL || 'http://localhost:8300',
  },

  // Output configuration
  output: 'standalone',
  
  // Disable x-powered-by header for security
  poweredByHeader: false,
};

module.exports = nextConfig;

# Universal API Proxy

This directory contains a universal API proxy that automatically forwards all `/api/*` requests to the backend Go API.

## Architecture

The universal proxy is implemented using Next.js dynamic routes at `/api/[...services]/route.ts` which:

- **Catches all API requests**: Any request to `/api/*` is automatically handled
- **Forwards to Go backend**: All requests are proxied to the backend running on port 8300
- **<PERSON>les authentication**: Automatically includes JWT tokens from NextAuth sessions
- **Supports all HTTP methods**: GET, POST, PUT, DELETE, PATCH, OPTIONS
- **Handles file uploads**: Special handling for multipart/form-data requests
- **Provides error handling**: Comprehensive error handling with proper status codes
- **Includes logging**: Request/response logging in development mode

## Why Use a Universal Proxy?

1. **Security**: Keeps backend API keys and sensitive data on the server side
2. **CORS**: Avoids CORS issues by making requests from the same origin
3. **Authentication**: <PERSON>les authentication tokens server-side automatically
4. **Error Handling**: Provides consistent error handling and response formatting
5. **Simplicity**: No need to create individual route files for each endpoint
6. **File Uploads**: Handles file uploads with proper timeout and content-type handling
7. **Development**: Built-in request/response logging for debugging

## How It Works

### Request Flow

1. **Client Request**: Frontend makes a request to `/api/users/me`
2. **Universal Proxy**: Next.js routes the request to `/api/[...services]/route.ts`
3. **Path Extraction**: Proxy extracts the service path (`/api/users/me`)
4. **Authentication**: Checks if endpoint requires auth and validates session
5. **Backend Request**: Forwards request to `http://localhost:8300/api/users/me`
6. **Response Handling**: Processes backend response and forwards to client

### Supported Request Types

- **JSON Requests**: Standard API requests with JSON payloads
- **File Uploads**: Multipart/form-data requests with proper timeout handling
- **Query Parameters**: Automatically forwarded to backend
- **Custom Headers**: Important headers are forwarded (user-agent, accept, etc.)

### Authentication

- **Public Endpoints**: No authentication required (health, auth endpoints)
- **Protected Endpoints**: Requires valid NextAuth session with access token
- **Automatic Headers**: JWT token automatically added to Authorization header

## Configuration

### Environment Variables

Set these in your `.env.local`:

```env
# Backend API URL (Go backend)
BACKEND_API_URL=http://localhost:8300

# NextAuth configuration
NEXTAUTH_URL=http://localhost:3300
NEXTAUTH_SECRET=your-secret-key
```

### Public Endpoints

These endpoints don't require authentication:

- `/health` - Health check
- `/api/public/*` - Public API endpoints
- `/api/auth/signin` - Sign in
- `/api/auth/signup` - Sign up
- `/api/auth/forgot-password` - Password reset
- `/api/auth/reset-password` - Password reset confirmation

### Upload Endpoints

These endpoints have extended timeouts for file uploads:

- `/api/storage/upload` - File uploads
- `/api/users/avatar` - User avatar upload
- `/api/organizations/logo` - Organization logo upload

## Usage Examples

### Frontend API Calls

Since the universal proxy handles everything automatically, you can make API calls directly:

```typescript
// In your React components or API endpoints
const response = await fetch('/api/users/me', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  },
});

const userData = await response.json();
```

### File Upload Example

```typescript
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/storage/upload', {
  method: 'POST',
  body: formData, // Don't set Content-Type, let browser set it
});
```

## Backend API Reference

The Go backend API is running on port 8300 and provides these endpoints:

- `/api/users/*` - User management
- `/api/organizations/*` - Organization management
- `/api/projects/*` - Project management
- `/api/translations/*` - Translation management
- `/api/locales/*` - Locale management
- `/api/ai-credits/*` - AI credits and pricing
- `/api/subscription-plans/*` - Subscription management
- `/api/auth/*` - Authentication
- `/health` - Health check (public)

## Current API Structure

After cleanup, the API directory contains only essential routes:

```
frontend/src/app/api/
├── README.md                           # This documentation
├── [...services]/route.ts              # Universal proxy (handles all /api/* requests)
├── auth/[...nextauth]/route.ts         # NextAuth authentication handler
├── create-checkout-session/route.ts    # Stripe checkout session creation
├── test-backend/route.ts               # Backend testing utility
└── webhooks/stripe/route.ts            # Stripe webhook handler
```

### Route Responsibilities

- **Universal Proxy**: Handles ALL standard API requests to backend
- **NextAuth**: Handles authentication flows and session management
- **Stripe Routes**: Handle payment processing with special Stripe SDK logic
- **Test Backend**: Development utility for testing backend connectivity

## Testing the Proxy

Use the Makefile command to test the proxy:

```bash
make proxy-test
```

This will test both public and authenticated endpoints to ensure the proxy is working correctly.

## Troubleshooting

### Common Issues

1. **503 Service Unavailable**: Backend is not running on port 8300
2. **401 Unauthorized**: No valid session or access token
3. **504 Timeout**: Request took too long (check backend performance)
4. **400 Bad Request**: Invalid request body or parameters

### Development Logging

In development mode, the proxy logs all requests and responses:

```
[API PROXY] GET /api/users/me
[API PROXY] GET /api/users/me -> 200 (150ms)
```

Check the browser console and terminal for detailed error messages.

# Universal API Proxy Setup

This document describes the universal API proxy implementation for the ADC Multi-Languages project.

## Overview

The universal API proxy is implemented using Next.js dynamic routes to automatically forward all `/api/*` requests from the frontend (port 3300) to the Go backend (port 8300).

## Architecture

```
Frontend (Next.js)     Universal Proxy        Backend (Go)
Port 3300         →    [...services]/route.ts  →    Port 8300

Client Request    →    Authentication Check   →    Backend API
/api/users/me     →    Add JWT Token          →    /api/users/me
                  →    Error Handling         →    
                  ←    Response Processing    ←    JSON Response
```

## Key Features

### 🔄 Universal Routing
- **Single Route Handler**: `/api/[...services]/route.ts` handles ALL API requests
- **Dynamic Path Mapping**: Automatically maps frontend paths to backend paths
- **All HTTP Methods**: Supports GET, POST, PUT, DELETE, PATCH, OPTIONS

### 🔐 Authentication
- **Automatic JWT Injection**: Adds Bearer tokens from NextAuth sessions
- **Public Endpoint Support**: Configurable endpoints that don't require auth
- **Session Validation**: Validates user sessions before forwarding requests

### 📁 File Upload Support
- **Multipart Form Data**: Handles file uploads with proper content-type
- **Extended Timeouts**: 60-second timeout for upload endpoints
- **Large File Support**: Streams files without loading into memory

### 🛡️ Error Handling
- **Comprehensive Error Types**: Handles network, timeout, and authentication errors
- **Proper Status Codes**: Returns appropriate HTTP status codes
- **Development Logging**: Detailed logging in development mode

### ⚡ Performance
- **Request Streaming**: Streams requests and responses for better performance
- **Header Forwarding**: Forwards important headers (user-agent, accept, etc.)
- **Response Caching**: Forwards cache headers from backend

## Implementation Files

### Core Files
- `frontend/src/app/api/[...services]/route.ts` - Universal proxy handler
- `frontend/next.config.js` - Next.js configuration
- `frontend/src/lib/api/proxy.ts` - Enhanced proxy utilities (legacy, kept for compatibility)

### Remaining API Routes (Special Purpose)
- `frontend/src/app/api/auth/[...nextauth]/route.ts` - NextAuth authentication
- `frontend/src/app/api/create-checkout-session/route.ts` - Stripe checkout with SDK
- `frontend/src/app/api/webhooks/stripe/route.ts` - Stripe webhook with signature verification
- `frontend/src/app/api/test-backend/route.ts` - Backend testing utility

### Configuration
- `backend-go/config/config.go` - Backend port configuration (8300)
- `Makefile` - Build and test commands

### Documentation
- `frontend/src/app/api/README.md` - Comprehensive API proxy documentation
- `frontend/test-proxy.js` - Automated test script

## Configuration

### Environment Variables

```env
# Backend API URL
BACKEND_API_URL=http://localhost:8300

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3300
NEXTAUTH_SECRET=your-secret-key
```

### Public Endpoints

These endpoints don't require authentication:
- `/health`
- `/api/public/*`
- `/api/auth/signin`
- `/api/auth/signup`
- `/api/auth/forgot-password`
- `/api/auth/reset-password`

### Upload Endpoints

These endpoints have extended timeouts:
- `/api/storage/upload`
- `/api/users/avatar`
- `/api/organizations/logo`

## Usage

### Starting Services

```bash
# Start both frontend and backend
make dev

# Or start individually
make frontend  # Port 3300
make backend   # Port 8300
```

### Testing the Proxy

```bash
# Comprehensive test
make proxy-test

# Simple curl test
make proxy-test-simple

# Check service status
make status
```

### Making API Calls

From the frontend, simply make requests to `/api/*`:

```typescript
// GET request
const response = await fetch('/api/users/me');
const user = await response.json();

// POST request
const response = await fetch('/api/organizations', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ name: 'My Org' }),
});

// File upload
const formData = new FormData();
formData.append('file', file);
const response = await fetch('/api/storage/upload', {
  method: 'POST',
  body: formData,
});
```

## Benefits

### For Developers
- **No Route Creation**: No need to create individual API route files
- **Automatic Auth**: Authentication is handled automatically
- **Type Safety**: Full TypeScript support
- **Error Handling**: Consistent error responses

### For Operations
- **Single Point of Control**: All API traffic goes through one handler
- **Centralized Logging**: All requests logged in one place
- **Easy Monitoring**: Simple to add metrics and monitoring
- **Security**: Centralized security controls

### For Performance
- **Reduced Bundle Size**: No individual route files
- **Streaming**: Efficient request/response streaming
- **Caching**: Proper cache header forwarding
- **Connection Pooling**: Efficient backend connections

## Troubleshooting

### Common Issues

1. **503 Service Unavailable**
   - Backend not running on port 8300
   - Check: `make status`

2. **401 Unauthorized**
   - No valid NextAuth session
   - User needs to log in

3. **504 Gateway Timeout**
   - Backend taking too long to respond
   - Check backend performance

4. **CORS Errors**
   - Should not occur with proxy
   - Check Next.js configuration

### Debug Mode

Enable detailed logging by setting:
```env
NODE_ENV=development
```

This will log all requests and responses to the console.

## Migration Notes

### From Individual Routes
- **Removed 24+ individual route files** that were simple proxies
- **Kept 4 special-purpose routes** with unique logic:
  - NextAuth authentication handler
  - Stripe checkout session creation
  - Stripe webhook with signature verification
  - Backend testing utility
- All standard API functionality moved to universal proxy
- Existing API calls continue to work without changes
- Cleaned up empty directories automatically

### Backward Compatibility
- All existing API endpoints continue to work
- No changes needed in frontend components
- Authentication flow remains the same

## Future Enhancements

- **Rate Limiting**: Add rate limiting per user/IP
- **Metrics Collection**: Add Prometheus metrics
- **Request Caching**: Add Redis caching layer
- **Load Balancing**: Support multiple backend instances
- **Circuit Breaker**: Add circuit breaker pattern for resilience
